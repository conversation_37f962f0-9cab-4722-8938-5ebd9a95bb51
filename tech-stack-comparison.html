<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>مقارنة التقنيات والمنصات</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Cairo", "Arial", sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        direction: rtl;
        padding: 20px;
      }

      .container {
        max-width: 1400px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        animation: fadeInUp 0.8s ease-out;
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 40px 30px;
        text-align: center;
        color: white;
        position: relative;
        overflow: hidden;
      }

      .header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.1) 0%,
          transparent 70%
        );
        animation: rotate 20s linear infinite;
      }

      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      h1 {
        font-size: clamp(24px, 4vw, 36px);
        font-weight: 700;
        margin-bottom: 10px;
        position: relative;
        z-index: 1;
      }

      .subtitle {
        font-size: clamp(14px, 2vw, 18px);
        opacity: 0.9;
        position: relative;
        z-index: 1;
      }

      .content {
        padding: 30px;
      }

      /* Desktop Table View */
      .table-container {
        overflow-x: auto;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        font-size: 14px;
        display: table;
      }

      th,
      td {
        padding: 15px 10px;
        text-align: center;
        vertical-align: middle;
        border-bottom: 1px solid #e0e6ed;
      }

      th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        font-size: 16px;
        position: sticky;
        top: 0;
        z-index: 10;
      }

      tr {
        transition: all 0.3s ease;
      }

      tr:hover {
        background-color: #f8f9ff;
        transform: scale(1.01);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      tr:nth-child(even) {
        background-color: #fafbfc;
      }

      .number {
        font-weight: bold;
        color: #e74c3c;
        font-size: 18px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
      }

      .price {
        font-size: 20px;
        filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
      }

      .tech-stack {
        font-weight: 600;
        color: #2980b9;
        background: linear-gradient(45deg, #3498db, #2980b9);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .description {
        color: #5a6c7d;
        font-weight: 400;
        line-height: 1.4;
      }

      /* Mobile Card View */
      .mobile-cards {
        display: none;
      }

      .card {
        background: white;
        border-radius: 15px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        border: 1px solid #e0e6ed;
      }

      .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      }

      .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        text-align: center;
      }

      .card-number {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .card-body {
        padding: 20px;
      }

      .card-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
      }

      .card-row:last-child {
        border-bottom: none;
      }

      .card-label {
        font-weight: 600;
        color: #2c3e50;
        flex: 1;
      }

      .card-value {
        flex: 2;
        text-align: left;
      }

      .card-value.tech-stack {
        font-weight: 600;
        color: #2980b9;
      }

      .card-value.price {
        font-size: 18px;
        text-align: center;
      }

      .card-value.description {
        color: #5a6c7d;
        line-height: 1.4;
        text-align: right;
      }

      /* Legend Styles */
      .legend {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 25px;
        margin-top: 30px;
        border: 1px solid #dee2e6;
      }

      .legend-title {
        font-size: 20px;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 15px;
        text-align: center;
      }

      .legend-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
      }

      .legend-item {
        background: white;
        padding: 12px 15px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .legend-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      }

      /* Responsive Design */
      @media (max-width: 1024px) {
        .content {
          padding: 20px;
        }

        th,
        td {
          padding: 10px 8px;
          font-size: 12px;
        }

        .header {
          padding: 30px 20px;
        }
      }

      @media (max-width: 768px) {
        body {
          padding: 10px;
        }

        .container {
          border-radius: 15px;
        }

        .header {
          padding: 25px 15px;
        }

        .content {
          padding: 15px;
        }

        /* Hide table on mobile */
        .table-container {
          display: none;
        }

        /* Show cards on mobile */
        .mobile-cards {
          display: block;
        }

        .legend-grid {
          grid-template-columns: 1fr;
        }
      }

      @media (max-width: 480px) {
        .card-row {
          flex-direction: column;
          align-items: flex-start;
          gap: 5px;
        }

        .card-label {
          font-size: 14px;
        }

        .card-value {
          text-align: right;
          width: 100%;
        }

        .card-value.price {
          text-align: center;
          font-size: 16px;
        }
      }

      @media print {
        body {
          background: white;
          padding: 0;
        }

        .container {
          box-shadow: none;
          background: white;
        }

        .header {
          background: #667eea !important;
          -webkit-print-color-adjust: exact;
        }

        .mobile-cards {
          display: none !important;
        }

        .table-container {
          display: block !important;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>مقارنة التقنيات والمنصات للمشاريع البرمجية</h1>
        <p class="subtitle">دليل شامل لاختيار التقنية المناسبة لمشروعك</p>
      </div>

      <div class="content">
        <!-- Desktop Table View -->
        <div class="table-container">
          <table>
            <thead>
              <tr>
                <th>الرقم</th>
                <th>الواجهة (Front)</th>
                <th>الخلفية (Back) / منصة</th>
                <th>نوع المشروع</th>
                <th>السعر النسبي 💰</th>
                <th>الوصف السريع للبيع</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="number">1️⃣</td>
                <td class="tech-stack">HTML + CSS + JS</td>
                <td class="tech-stack">PHP + Laravel</td>
                <td>موقع بسيط / شركة</td>
                <td class="price">💸</td>
                <td class="description">
                  موقع مخصص وسريع – تعريف شركة أو خدمات
                </td>
              </tr>
              <tr>
                <td class="number">2️⃣</td>
                <td class="tech-stack">React.js</td>
                <td class="tech-stack">Node.js + Express</td>
                <td>Web App / متجر إلكتروني</td>
                <td class="price">💵💵</td>
                <td class="description">حديث، سريع، قابل للتوسعة</td>
              </tr>
              <tr>
                <td class="number">3️⃣</td>
                <td class="tech-stack">Vue.js</td>
                <td class="tech-stack">Laravel</td>
                <td>نظام متوسط – مرونة جيدة</td>
                <td class="price">💵💵</td>
                <td class="description">مزيج ممتاز لمواقع بإدارة بسيطة</td>
              </tr>
              <tr>
                <td class="number">4️⃣</td>
                <td class="tech-stack">HTML + CSS + JS</td>
                <td class="tech-stack">Python + Flask</td>
                <td>موقع بسيط فيه تسجيل دخول</td>
                <td class="price">💵</td>
                <td class="description">كويس لمواقع العضويات الخفيفة</td>
              </tr>
              <tr>
                <td class="number">5️⃣</td>
                <td class="tech-stack">React.js + Tailwind CSS</td>
                <td class="tech-stack">Django (Python)</td>
                <td>Web App متكامل</td>
                <td class="price">💵💵💵</td>
                <td class="description">
                  مثالي للأنظمة الإدارية ولوحات التحكم
                </td>
              </tr>
              <tr>
                <td class="number">6️⃣</td>
                <td class="tech-stack">Angular</td>
                <td class="tech-stack">Java + Spring Boot</td>
                <td>تطبيق حكومي / مؤسسي</td>
                <td class="price">💰💰💰💰</td>
                <td class="description">للأنظمة المعقدة والمؤمنة جداً</td>
              </tr>
              <tr>
                <td class="number">7️⃣</td>
                <td class="tech-stack">Next.js</td>
                <td class="tech-stack">Node.js + MongoDB</td>
                <td>موقع ديناميكي مع SEO عالي</td>
                <td class="price">💵💵</td>
                <td class="description">
                  موقع حديث وسريع، مثالي لمحركات البحث
                </td>
              </tr>
              <tr>
                <td class="number">8️⃣</td>
                <td class="tech-stack">React.js</td>
                <td class="tech-stack">Firebase</td>
                <td>MVP سريع / تطبيق موبايل</td>
                <td class="price">💵</td>
                <td class="description">
                  بدون سيرفر، للتجارب والمشاريع الصغيرة
                </td>
              </tr>
              <tr>
                <td class="number">9️⃣</td>
                <td class="tech-stack">HTML + Bootstrap</td>
                <td class="tech-stack">لا يوجد Back-End</td>
                <td>صفحة ثابتة / Landing Page</td>
                <td class="price">💸</td>
                <td class="description">أرخص وأسرع حل لحملات إعلانية</td>
              </tr>
              <tr>
                <td class="number">🔟</td>
                <td class="tech-stack">WordPress (تصميم جاهز أو مخصص)</td>
                <td class="tech-stack">PHP</td>
                <td>موقع محتوى / مدونة / شركة</td>
                <td class="price">💵</td>
                <td class="description">سهل وسريع – العميل يقدر يديره بنفسه</td>
              </tr>
              <tr>
                <td class="number">1️⃣1️⃣</td>
                <td class="tech-stack">WooCommerce (إضافة للوردبريس)</td>
                <td class="tech-stack">WordPress + PHP</td>
                <td>متجر إلكتروني بسيط</td>
                <td class="price">💵💵</td>
                <td class="description">مثالي للمشاريع الصغيرة والمتوسطة</td>
              </tr>
              <tr>
                <td class="number">1️⃣2️⃣</td>
                <td class="tech-stack">Shopify</td>
                <td class="tech-stack">SaaS / منصة جاهزة</td>
                <td>متجر جاهز وسريع التشغيل</td>
                <td class="price">💵💵💵</td>
                <td class="description">أسرع حل للبيع أونلاين – بدون برمجة</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Mobile Card View -->
        <div class="mobile-cards">
          <div class="card">
            <div class="card-header">
              <div class="card-number">1️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">الواجهة:</div>
                <div class="card-value tech-stack">HTML + CSS + JS</div>
              </div>
              <div class="card-row">
                <div class="card-label">الخلفية:</div>
                <div class="card-value tech-stack">PHP + Laravel</div>
              </div>
              <div class="card-row">
                <div class="card-label">نوع المشروع:</div>
                <div class="card-value">موقع بسيط / شركة</div>
              </div>
              <div class="card-row">
                <div class="card-label">السعر:</div>
                <div class="card-value price">💸</div>
              </div>
              <div class="card-row">
                <div class="card-label">الوصف:</div>
                <div class="card-value description">
                  موقع مخصص وسريع – تعريف شركة أو خدمات
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">2️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">الواجهة:</div>
                <div class="card-value tech-stack">React.js</div>
              </div>
              <div class="card-row">
                <div class="card-label">الخلفية:</div>
                <div class="card-value tech-stack">Node.js + Express</div>
              </div>
              <div class="card-row">
                <div class="card-label">نوع المشروع:</div>
                <div class="card-value">Web App / متجر إلكتروني</div>
              </div>
              <div class="card-row">
                <div class="card-label">السعر:</div>
                <div class="card-value price">💵💵</div>
              </div>
              <div class="card-row">
                <div class="card-label">الوصف:</div>
                <div class="card-value description">
                  حديث، سريع، قابل للتوسعة
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">3️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">الواجهة:</div>
                <div class="card-value tech-stack">Vue.js</div>
              </div>
              <div class="card-row">
                <div class="card-label">الخلفية:</div>
                <div class="card-value tech-stack">Laravel</div>
              </div>
              <div class="card-row">
                <div class="card-label">نوع المشروع:</div>
                <div class="card-value">نظام متوسط – مرونة جيدة</div>
              </div>
              <div class="card-row">
                <div class="card-label">السعر:</div>
                <div class="card-value price">💵💵</div>
              </div>
              <div class="card-row">
                <div class="card-label">الوصف:</div>
                <div class="card-value description">
                  مزيج ممتاز لمواقع بإدارة بسيطة
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">4️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">الواجهة:</div>
                <div class="card-value tech-stack">HTML + CSS + JS</div>
              </div>
              <div class="card-row">
                <div class="card-label">الخلفية:</div>
                <div class="card-value tech-stack">Python + Flask</div>
              </div>
              <div class="card-row">
                <div class="card-label">نوع المشروع:</div>
                <div class="card-value">موقع بسيط فيه تسجيل دخول</div>
              </div>
              <div class="card-row">
                <div class="card-label">السعر:</div>
                <div class="card-value price">💵</div>
              </div>
              <div class="card-row">
                <div class="card-label">الوصف:</div>
                <div class="card-value description">
                  كويس لمواقع العضويات الخفيفة
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">5️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">الواجهة:</div>
                <div class="card-value tech-stack">React.js + Tailwind CSS</div>
              </div>
              <div class="card-row">
                <div class="card-label">الخلفية:</div>
                <div class="card-value tech-stack">Django (Python)</div>
              </div>
              <div class="card-row">
                <div class="card-label">نوع المشروع:</div>
                <div class="card-value">Web App متكامل</div>
              </div>
              <div class="card-row">
                <div class="card-label">السعر:</div>
                <div class="card-value price">💵💵💵</div>
              </div>
              <div class="card-row">
                <div class="card-label">الوصف:</div>
                <div class="card-value description">
                  مثالي للأنظمة الإدارية ولوحات التحكم
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">6️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">الواجهة:</div>
                <div class="card-value tech-stack">Angular</div>
              </div>
              <div class="card-row">
                <div class="card-label">الخلفية:</div>
                <div class="card-value tech-stack">Java + Spring Boot</div>
              </div>
              <div class="card-row">
                <div class="card-label">نوع المشروع:</div>
                <div class="card-value">تطبيق حكومي / مؤسسي</div>
              </div>
              <div class="card-row">
                <div class="card-label">السعر:</div>
                <div class="card-value price">💰💰💰💰</div>
              </div>
              <div class="card-row">
                <div class="card-label">الوصف:</div>
                <div class="card-value description">
                  للأنظمة المعقدة والمؤمنة جداً
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">7️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">الواجهة:</div>
                <div class="card-value tech-stack">Next.js</div>
              </div>
              <div class="card-row">
                <div class="card-label">الخلفية:</div>
                <div class="card-value tech-stack">Node.js + MongoDB</div>
              </div>
              <div class="card-row">
                <div class="card-label">نوع المشروع:</div>
                <div class="card-value">موقع ديناميكي مع SEO عالي</div>
              </div>
              <div class="card-row">
                <div class="card-label">السعر:</div>
                <div class="card-value price">💵💵</div>
              </div>
              <div class="card-row">
                <div class="card-label">الوصف:</div>
                <div class="card-value description">
                  موقع حديث وسريع، مثالي لمحركات البحث
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">8️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">الواجهة:</div>
                <div class="card-value tech-stack">React.js</div>
              </div>
              <div class="card-row">
                <div class="card-label">الخلفية:</div>
                <div class="card-value tech-stack">Firebase</div>
              </div>
              <div class="card-row">
                <div class="card-label">نوع المشروع:</div>
                <div class="card-value">MVP سريع / تطبيق موبايل</div>
              </div>
              <div class="card-row">
                <div class="card-label">السعر:</div>
                <div class="card-value price">💵</div>
              </div>
              <div class="card-row">
                <div class="card-label">الوصف:</div>
                <div class="card-value description">
                  بدون سيرفر، للتجارب والمشاريع الصغيرة
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">9️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">الواجهة:</div>
                <div class="card-value tech-stack">HTML + Bootstrap</div>
              </div>
              <div class="card-row">
                <div class="card-label">الخلفية:</div>
                <div class="card-value tech-stack">لا يوجد Back-End</div>
              </div>
              <div class="card-row">
                <div class="card-label">نوع المشروع:</div>
                <div class="card-value">صفحة ثابتة / Landing Page</div>
              </div>
              <div class="card-row">
                <div class="card-label">السعر:</div>
                <div class="card-value price">💸</div>
              </div>
              <div class="card-row">
                <div class="card-label">الوصف:</div>
                <div class="card-value description">
                  أرخص وأسرع حل لحملات إعلانية
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">🔟</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">الواجهة:</div>
                <div class="card-value tech-stack">
                  WordPress (تصميم جاهز أو مخصص)
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">الخلفية:</div>
                <div class="card-value tech-stack">PHP</div>
              </div>
              <div class="card-row">
                <div class="card-label">نوع المشروع:</div>
                <div class="card-value">موقع محتوى / مدونة / شركة</div>
              </div>
              <div class="card-row">
                <div class="card-label">السعر:</div>
                <div class="card-value price">💵</div>
              </div>
              <div class="card-row">
                <div class="card-label">الوصف:</div>
                <div class="card-value description">
                  سهل وسريع – العميل يقدر يديره بنفسه
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">1️⃣1️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">الواجهة:</div>
                <div class="card-value tech-stack">
                  WooCommerce (إضافة للوردبريس)
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">الخلفية:</div>
                <div class="card-value tech-stack">WordPress + PHP</div>
              </div>
              <div class="card-row">
                <div class="card-label">نوع المشروع:</div>
                <div class="card-value">متجر إلكتروني بسيط</div>
              </div>
              <div class="card-row">
                <div class="card-label">السعر:</div>
                <div class="card-value price">💵💵</div>
              </div>
              <div class="card-row">
                <div class="card-label">الوصف:</div>
                <div class="card-value description">
                  مثالي للمشاريع الصغيرة والمتوسطة
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">1️⃣2️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">الواجهة:</div>
                <div class="card-value tech-stack">Shopify</div>
              </div>
              <div class="card-row">
                <div class="card-label">الخلفية:</div>
                <div class="card-value tech-stack">SaaS / منصة جاهزة</div>
              </div>
              <div class="card-row">
                <div class="card-label">نوع المشروع:</div>
                <div class="card-value">متجر جاهز وسريع التشغيل</div>
              </div>
              <div class="card-row">
                <div class="card-label">السعر:</div>
                <div class="card-value price">💵💵💵</div>
              </div>
              <div class="card-row">
                <div class="card-label">الوصف:</div>
                <div class="card-value description">
                  أسرع حل للبيع أونلاين – بدون برمجة
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Price Legend -->
        <div class="legend">
          <div class="legend-title">مفتاح الأسعار</div>
          <div class="legend-grid">
            <div class="legend-item">💸 منخفض جداً</div>
            <div class="legend-item">💵 منخفض</div>
            <div class="legend-item">💵💵 متوسط</div>
            <div class="legend-item">💵💵💵 مرتفع</div>
            <div class="legend-item">💰💰💰💰 مرتفع جداً</div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
