<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مقارنة التقنيات والمنصات</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 28px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }
        th, td {
            border: 2px solid #3498db;
            padding: 12px;
            text-align: center;
            vertical-align: middle;
        }
        th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        tr:nth-child(odd) {
            background-color: #ffffff;
        }
        .number {
            font-weight: bold;
            color: #e74c3c;
            font-size: 16px;
        }
        .price {
            font-size: 18px;
        }
        .tech-stack {
            font-weight: bold;
            color: #2980b9;
        }
        .description {
            font-style: italic;
            color: #7f8c8d;
        }
        @media print {
            body {
                background-color: white;
            }
            .container {
                box-shadow: none;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>مقارنة التقنيات والمنصات للمشاريع البرمجية</h1>
        
        <table>
            <thead>
                <tr>
                    <th>الرقم</th>
                    <th>الواجهة (Front)</th>
                    <th>الخلفية (Back) / منصة</th>
                    <th>نوع المشروع</th>
                    <th>السعر النسبي 💰</th>
                    <th>الوصف السريع للبيع</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="number">1️⃣</td>
                    <td class="tech-stack">HTML + CSS + JS</td>
                    <td class="tech-stack">PHP + Laravel</td>
                    <td>موقع بسيط / شركة</td>
                    <td class="price">💸</td>
                    <td class="description">موقع مخصص وسريع – تعريف شركة أو خدمات</td>
                </tr>
                <tr>
                    <td class="number">2️⃣</td>
                    <td class="tech-stack">React.js</td>
                    <td class="tech-stack">Node.js + Express</td>
                    <td>Web App / متجر إلكتروني</td>
                    <td class="price">💵💵</td>
                    <td class="description">حديث، سريع، قابل للتوسعة</td>
                </tr>
                <tr>
                    <td class="number">3️⃣</td>
                    <td class="tech-stack">Vue.js</td>
                    <td class="tech-stack">Laravel</td>
                    <td>نظام متوسط – مرونة جيدة</td>
                    <td class="price">💵💵</td>
                    <td class="description">مزيج ممتاز لمواقع بإدارة بسيطة</td>
                </tr>
                <tr>
                    <td class="number">4️⃣</td>
                    <td class="tech-stack">HTML + CSS + JS</td>
                    <td class="tech-stack">Python + Flask</td>
                    <td>موقع بسيط فيه تسجيل دخول</td>
                    <td class="price">💵</td>
                    <td class="description">كويس لمواقع العضويات الخفيفة</td>
                </tr>
                <tr>
                    <td class="number">5️⃣</td>
                    <td class="tech-stack">React.js + Tailwind CSS</td>
                    <td class="tech-stack">Django (Python)</td>
                    <td>Web App متكامل</td>
                    <td class="price">💵💵💵</td>
                    <td class="description">مثالي للأنظمة الإدارية ولوحات التحكم</td>
                </tr>
                <tr>
                    <td class="number">6️⃣</td>
                    <td class="tech-stack">Angular</td>
                    <td class="tech-stack">Java + Spring Boot</td>
                    <td>تطبيق حكومي / مؤسسي</td>
                    <td class="price">💰💰💰💰</td>
                    <td class="description">للأنظمة المعقدة والمؤمنة جدًا</td>
                </tr>
                <tr>
                    <td class="number">7️⃣</td>
                    <td class="tech-stack">Next.js</td>
                    <td class="tech-stack">Node.js + MongoDB</td>
                    <td>موقع ديناميكي مع SEO عالي</td>
                    <td class="price">💵💵</td>
                    <td class="description">موقع حديث وسريع، مثالي لمحركات البحث</td>
                </tr>
                <tr>
                    <td class="number">8️⃣</td>
                    <td class="tech-stack">React.js</td>
                    <td class="tech-stack">Firebase</td>
                    <td>MVP سريع / تطبيق موبايل</td>
                    <td class="price">💵</td>
                    <td class="description">بدون سيرفر، للتجارب والمشاريع الصغيرة</td>
                </tr>
                <tr>
                    <td class="number">9️⃣</td>
                    <td class="tech-stack">HTML + Bootstrap</td>
                    <td class="tech-stack">لا يوجد Back-End</td>
                    <td>صفحة ثابتة / Landing Page</td>
                    <td class="price">💸</td>
                    <td class="description">أرخص وأسرع حل لحملات إعلانية</td>
                </tr>
                <tr>
                    <td class="number">🔟</td>
                    <td class="tech-stack">WordPress (تصميم جاهز أو مخصص)</td>
                    <td class="tech-stack">PHP</td>
                    <td>موقع محتوى / مدونة / شركة</td>
                    <td class="price">💵</td>
                    <td class="description">سهل وسريع – العميل يقدر يديره بنفسه</td>
                </tr>
                <tr>
                    <td class="number">1️⃣1️⃣</td>
                    <td class="tech-stack">WooCommerce (إضافة للوردبريس)</td>
                    <td class="tech-stack">WordPress + PHP</td>
                    <td>متجر إلكتروني بسيط</td>
                    <td class="price">💵💵</td>
                    <td class="description">مثالي للمشاريع الصغيرة والمتوسطة</td>
                </tr>
                <tr>
                    <td class="number">1️⃣2️⃣</td>
                    <td class="tech-stack">Shopify</td>
                    <td class="tech-stack">SaaS / منصة جاهزة</td>
                    <td>متجر جاهز وسريع التشغيل</td>
                    <td class="price">💵💵💵</td>
                    <td class="description">أسرع حل للبيع أونلاين – بدون برمجة</td>
                </tr>
            </tbody>
        </table>
        
        <div style="margin-top: 30px; padding: 20px; background-color: #ecf0f1; border-radius: 5px;">
            <h3 style="color: #2c3e50; margin-bottom: 10px;">مفتاح الأسعار:</h3>
            <p><strong>💸</strong> - منخفض جداً</p>
            <p><strong>💵</strong> - منخفض</p>
            <p><strong>💵💵</strong> - متوسط</p>
            <p><strong>💵💵💵</strong> - مرتفع</p>
            <p><strong>💰💰💰💰</strong> - مرتفع جداً</p>
        </div>
    </div>
</body>
</html>
