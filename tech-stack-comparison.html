<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>دليل التقنيات للمشاريع التجارية - اختار الأنسب لبيزنسك</title>
    <link
      href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "IBM Plex Sans Arabic", "Arial", sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        direction: rtl;
        padding: 20px;
      }

      .container {
        max-width: 1400px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        animation: fadeInUp 0.8s ease-out;
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 40px 30px;
        text-align: center;
        color: white;
        position: relative;
        overflow: hidden;
      }

      .logo {
        max-width: 120px;
        height: auto;
        margin-bottom: 20px;
        filter: brightness(0) invert(1);
        position: relative;
        z-index: 2;
      }

      .header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.1) 0%,
          transparent 70%
        );
        animation: rotate 20s linear infinite;
      }

      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      h1 {
        font-size: clamp(24px, 4vw, 36px);
        font-weight: 700;
        margin-bottom: 10px;
        position: relative;
        z-index: 1;
      }

      .subtitle {
        font-size: clamp(14px, 2vw, 18px);
        opacity: 0.9;
        position: relative;
        z-index: 1;
      }

      .content {
        padding: 30px;
      }

      /* Desktop Table View */
      .table-container {
        overflow-x: auto;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        font-size: 14px;
        display: table;
      }

      th,
      td {
        padding: 15px 10px;
        text-align: center;
        vertical-align: middle;
        border-bottom: 1px solid #e0e6ed;
      }

      th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        font-size: 16px;
        position: sticky;
        top: 0;
        z-index: 10;
      }

      tr {
        transition: all 0.3s ease;
      }

      tr:hover {
        background-color: #f8f9ff;
        transform: scale(1.01);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      tr:nth-child(even) {
        background-color: #fafbfc;
      }

      .number {
        font-weight: bold;
        color: #e74c3c;
        font-size: 18px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
      }

      .tech-stack {
        font-weight: 600;
        color: #2980b9;
        background: linear-gradient(45deg, #3498db, #2980b9);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .description {
        color: #5a6c7d;
        font-weight: 400;
        line-height: 1.4;
      }

      /* Mobile Card View */
      .mobile-cards {
        display: none;
      }

      .card {
        background: white;
        border-radius: 15px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        border: 1px solid #e0e6ed;
      }

      .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      }

      .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        text-align: center;
      }

      .card-number {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .card-body {
        padding: 20px;
      }

      .card-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
      }

      .card-row:last-child {
        border-bottom: none;
      }

      .card-label {
        font-weight: 600;
        color: #2c3e50;
        flex: 1;
      }

      .card-value {
        flex: 2;
        text-align: left;
      }

      .card-value.tech-stack {
        font-weight: 600;
        color: #2980b9;
      }

      .card-value.description {
        color: #5a6c7d;
        line-height: 1.4;
        text-align: right;
      }

      /* Responsive Design */
      @media (max-width: 1024px) {
        .content {
          padding: 20px;
        }

        th,
        td {
          padding: 10px 8px;
          font-size: 12px;
        }

        .header {
          padding: 30px 20px;
        }

        .logo {
          max-width: 100px;
        }
      }

      @media (max-width: 768px) {
        body {
          padding: 10px;
        }

        .container {
          border-radius: 15px;
        }

        .header {
          padding: 25px 15px;
        }

        .logo {
          max-width: 80px;
        }

        .content {
          padding: 15px;
        }

        /* Hide table on mobile */
        .table-container {
          display: none;
        }

        /* Show cards on mobile */
        .mobile-cards {
          display: block;
        }
      }

      @media (max-width: 480px) {
        .card-row {
          flex-direction: column;
          align-items: flex-start;
          gap: 5px;
        }

        .card-label {
          font-size: 14px;
        }

        .card-value {
          text-align: right;
          width: 100%;
        }
      }

      @media print {
        body {
          background: white;
          padding: 0;
        }

        .container {
          box-shadow: none;
          background: white;
        }

        .header {
          background: #667eea !important;
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }

        .mobile-cards {
          display: none !important;
        }

        .table-container {
          display: block !important;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <img
          src="ecom_logo-removebg-preview.png"
          alt="شعار الشركة"
          class="logo"
        />
        <h1>دليل التقنيات للمشاريع التجارية</h1>
        <p class="subtitle">
          اختار التقنية اللي هتخلي بيزنسك يتفوق على منافسينك وتزود أرباحك
        </p>
      </div>

      <div class="content">
        <!-- Desktop Table View -->
        <div class="table-container">
          <table>
            <thead>
              <tr>
                <th>الرقم</th>
                <th>التقنية</th>
                <th>مناسب للمجالات</th>
                <th>المميزات الأساسية</th>
                <th>وقت التطوير</th>
                <th>التوصية</th>
              </tr>
            </thead>
            <tbody>
              <!-- Group 1: HTML+CSS+JS Projects -->
              <tr>
                <td class="number">1️⃣</td>
                <td class="tech-stack">HTML + CSS + JS + Laravel</td>
                <td>الشركات، المكاتب، العيادات، المحاماة، الاستشارات</td>
                <td class="description">
                  موقع تعريفي احترافي، سرعة عالية، أمان ممتاز، سهولة التحديث
                </td>
                <td>2-3 أسابيع</td>
                <td>مثالي للشركات الجديدة والمهن الحرة</td>
              </tr>
              <tr>
                <td class="number">2️⃣</td>
                <td class="tech-stack">HTML + CSS + JS + Flask</td>
                <td>النوادي، الجمعيات، المراكز التعليمية، الكورسات</td>
                <td class="description">
                  نظام عضويات بسيط، تسجيل دخول آمن، إدارة المستخدمين
                </td>
                <td>1-2 أسبوع</td>
                <td>ممتاز للمشاريع الصغيرة بميزانية محدودة</td>
              </tr>
              <tr>
                <td class="number">3️⃣</td>
                <td class="tech-stack">HTML + Bootstrap</td>
                <td>الحملات الإعلانية، المنتجات الجديدة، الفعاليات</td>
                <td class="description">
                  سرعة تحميل فائقة، تصميم جذاب، تحسين للإعلانات
                </td>
                <td>3-5 أيام</td>
                <td>الأسرع والأوفر للحملات التسويقية</td>
              </tr>
              <!-- Group 2: React.js Projects -->
              <tr>
                <td class="number">4️⃣</td>
                <td class="tech-stack">React.js + Node.js</td>
                <td>المتاجر الإلكترونية، التطبيقات التفاعلية، البنوك</td>
                <td class="description">
                  سرعة استجابة عالية، تجربة مستخدم متقدمة، أداء ممتاز
                </td>
                <td>4-6 أسابيع</td>
                <td>الأفضل للمتاجر والتطبيقات الحديثة</td>
              </tr>
              <tr>
                <td class="number">5️⃣</td>
                <td class="tech-stack">React.js + Django</td>
                <td>الأنظمة الإدارية، CRM، إدارة المخازن، الحسابات</td>
                <td class="description">
                  لوحات تحكم قوية، تقارير متقدمة، إدارة شاملة للبيانات
                </td>
                <td>6-8 أسابيع</td>
                <td>مثالي للشركات المتوسطة والكبيرة</td>
              </tr>
              <tr>
                <td class="number">6️⃣</td>
                <td class="tech-stack">React.js + Firebase</td>
                <td>الستارت أب، التطبيقات التجريبية، المشاريع السريعة</td>
                <td class="description">
                  تطوير سريع، تكلفة منخفضة، قابلية توسع تلقائية
                </td>
                <td>2-3 أسابيع</td>
                <td>ممتاز لاختبار الأفكار الجديدة</td>
              </tr>
              <!-- Group 3: Vue.js Projects -->
              <tr>
                <td class="number">7️⃣</td>
                <td class="tech-stack">Vue.js + Laravel</td>
                <td>
                  المواقع التفاعلية، البوابات الإلكترونية، الأنظمة المتوسطة
                </td>
                <td class="description">
                  توازن مثالي بين السهولة والقوة، مرونة في التطوير
                </td>
                <td>3-5 أسابيع</td>
                <td>مناسب للمشاريع المتوسطة القابلة للتوسع</td>
              </tr>
              <!-- Group 4: Next.js Projects -->
              <tr>
                <td class="number">8️⃣</td>
                <td class="tech-stack">Next.js + MongoDB</td>
                <td>المواقع التجارية، البلوجز، المجلات الإلكترونية</td>
                <td class="description">
                  تحسين محركات البحث، سرعة عالية، ظهور متقدم في جوجل
                </td>
                <td>4-6 أسابيع</td>
                <td>الأفضل للمواقع التي تعتمد على الزوار</td>
              </tr>
              <!-- Group 5: Angular Projects -->
              <tr>
                <td class="number">9️⃣</td>
                <td class="tech-stack">Angular + Java</td>
                <td>البنوك، الحكومة، المؤسسات الكبيرة، الأنظمة المعقدة</td>
                <td class="description">
                  أمان عالي جداً، استقرار ممتاز، مناسب للبيانات الحساسة
                </td>
                <td>8-12 أسبوع</td>
                <td>للمؤسسات التي تحتاج أقصى درجات الأمان</td>
              </tr>
              <!-- Group 6: WordPress/CMS Projects -->
              <tr>
                <td class="number">🔟</td>
                <td class="tech-stack">WordPress</td>
                <td>المدونات، المواقع الإخبارية، الشركات الصغيرة</td>
                <td class="description">
                  سهولة الإدارة، تحديث المحتوى بنفسك، تكلفة صيانة قليلة
                </td>
                <td>1-2 أسبوع</td>
                <td>الأنسب لمن يريد إدارة موقعه بنفسه</td>
              </tr>
              <tr>
                <td class="number">1️⃣1️⃣</td>
                <td class="tech-stack">WooCommerce</td>
                <td>المتاجر الصغيرة، بيع المنتجات، التجارة الإلكترونية</td>
                <td class="description">
                  متجر إلكتروني متكامل، إدارة المخزون، طرق دفع متعددة
                </td>
                <td>2-3 أسابيع</td>
                <td>الأوفر للمتاجر الصغيرة والمتوسطة</td>
              </tr>
              <tr>
                <td class="number">1️⃣2️⃣</td>
                <td class="tech-stack">Shopify</td>
                <td>التجارة السريعة، البيع الدولي، الدروب شيبنج</td>
                <td class="description">
                  جاهز للاستخدام فوراً، دعم فني ممتاز، أمان عالي
                </td>
                <td>3-7 أيام</td>
                <td>الأسرع للبدء في التجارة الإلكترونية</td>
              </tr>
              <!-- Group 7: Modern Technologies -->
              <tr>
                <td class="number">1️⃣3️⃣</td>
                <td class="tech-stack">Flutter + Firebase</td>
                <td>تطبيقات الموبايل، التطبيقات متعددة المنصات</td>
                <td class="description">
                  تطبيق واحد يعمل على أندرويد و iOS، توفير في التكلفة
                </td>
                <td>6-10 أسابيع</td>
                <td>الأفضل لتطبيقات الموبايل الحديثة</td>
              </tr>
              <tr>
                <td class="number">1️⃣4️⃣</td>
                <td class="tech-stack">Svelte + SvelteKit</td>
                <td>المواقع فائقة السرعة، التطبيقات الخفيفة</td>
                <td class="description">
                  أسرع تقنية حالياً، استهلاك أقل للموارد، أداء ممتاز
                </td>
                <td>3-5 أسابيع</td>
                <td>للمشاريع التي تحتاج سرعة قصوى</td>
              </tr>
              <tr>
                <td class="number">1️⃣5️⃣</td>
                <td class="tech-stack">Nuxt.js + Supabase</td>
                <td>التطبيقات الحديثة، الستارت أب التقنية</td>
                <td class="description">
                  تقنية متقدمة، قاعدة بيانات سحابية، تطوير سريع
                </td>
                <td>4-7 أسابيع</td>
                <td>للمشاريع التقنية المتقدمة</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Mobile Card View -->
        <div class="mobile-cards">
          <!-- Group 1: HTML+CSS+JS Projects -->
          <div class="card">
            <div class="card-header">
              <div class="card-number">1️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">
                  HTML + CSS + JS + Laravel
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  الشركات، المكاتب، العيادات، المحاماة، الاستشارات
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  موقع تعريفي احترافي، سرعة عالية، أمان ممتاز، سهولة التحديث
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">2-3 أسابيع</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">مثالي للشركات الجديدة والمهن الحرة</div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">2️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">HTML + CSS + JS + Flask</div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  النوادي، الجمعيات، المراكز التعليمية، الكورسات
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  نظام عضويات بسيط، تسجيل دخول آمن، إدارة المستخدمين
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">1-2 أسبوع</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">
                  ممتاز للمشاريع الصغيرة بميزانية محدودة
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">3️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">HTML + Bootstrap</div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  الحملات الإعلانية، المنتجات الجديدة، الفعاليات
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  سرعة تحميل فائقة، تصميم جذاب، تحسين للإعلانات
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">3-5 أيام</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">الأسرع والأوفر للحملات التسويقية</div>
              </div>
            </div>
          </div>

          <!-- Group 2: React.js Projects -->
          <div class="card">
            <div class="card-header">
              <div class="card-number">4️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">React.js + Node.js</div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  المتاجر الإلكترونية، التطبيقات التفاعلية، البنوك
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  سرعة استجابة عالية، تجربة مستخدم متقدمة، أداء ممتاز
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">4-6 أسابيع</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">الأفضل للمتاجر والتطبيقات الحديثة</div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">5️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">React.js + Django</div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  الأنظمة الإدارية، CRM، إدارة المخازن، الحسابات
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  لوحات تحكم قوية، تقارير متقدمة، إدارة شاملة للبيانات
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">6-8 أسابيع</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">مثالي للشركات المتوسطة والكبيرة</div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">6️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">React.js + Firebase</div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  الستارت أب، التطبيقات التجريبية، المشاريع السريعة
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  تطوير سريع، تكلفة منخفضة، قابلية توسع تلقائية
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">2-3 أسابيع</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">ممتاز لاختبار الأفكار الجديدة</div>
              </div>
            </div>
          </div>

          <!-- باقي الكروت بالتنسيق الجديد -->
          <!-- Group 3: Vue.js Projects -->
          <div class="card">
            <div class="card-header">
              <div class="card-number">7️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">Vue.js + Laravel</div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  المواقع التفاعلية، البوابات الإلكترونية، الأنظمة المتوسطة
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  توازن مثالي بين السهولة والقوة، مرونة في التطوير
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">3-5 أسابيع</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">
                  مناسب للمشاريع المتوسطة القابلة للتوسع
                </div>
              </div>
            </div>
          </div>

          <!-- Group 4: Next.js Projects -->
          <div class="card">
            <div class="card-header">
              <div class="card-number">8️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">Next.js + MongoDB</div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  المواقع التجارية، البلوجز، المجلات الإلكترونية
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  تحسين محركات البحث، سرعة عالية، ظهور متقدم في جوجل
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">4-6 أسابيع</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">
                  الأفضل للمواقع التي تعتمد على الزوار
                </div>
              </div>
            </div>
          </div>

          <!-- Group 5: Angular Projects -->
          <div class="card">
            <div class="card-header">
              <div class="card-number">9️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">Angular + Java</div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  البنوك، الحكومة، المؤسسات الكبيرة، الأنظمة المعقدة
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  أمان عالي جداً، استقرار ممتاز، مناسب للبيانات الحساسة
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">8-12 أسبوع</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">
                  للمؤسسات التي تحتاج أقصى درجات الأمان
                </div>
              </div>
            </div>
          </div>

          <!-- Group 6: WordPress/CMS Projects -->
          <div class="card">
            <div class="card-header">
              <div class="card-number">🔟</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">WordPress</div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  المدونات، المواقع الإخبارية، الشركات الصغيرة
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  سهولة الإدارة، تحديث المحتوى بنفسك، تكلفة صيانة قليلة
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">1-2 أسبوع</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">الأنسب لمن يريد إدارة موقعه بنفسه</div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">1️⃣1️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">WooCommerce</div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  المتاجر الصغيرة، بيع المنتجات، التجارة الإلكترونية
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  متجر إلكتروني متكامل، إدارة المخزون، طرق دفع متعددة
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">2-3 أسابيع</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">الأوفر للمتاجر الصغيرة والمتوسطة</div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">1️⃣2️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">Shopify</div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  التجارة السريعة، البيع الدولي، الدروب شيبنج
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  جاهز للاستخدام فوراً، دعم فني ممتاز، أمان عالي
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">3-7 أيام</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">
                  الأسرع للبدء في التجارة الإلكترونية
                </div>
              </div>
            </div>
          </div>

          <!-- Group 7: Modern Technologies -->
          <div class="card">
            <div class="card-header">
              <div class="card-number">1️⃣3️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">Flutter + Firebase</div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  تطبيقات الموبايل، التطبيقات متعددة المنصات
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  تطبيق واحد يعمل على أندرويد و iOS، توفير في التكلفة
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">6-10 أسابيع</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">الأفضل لتطبيقات الموبايل الحديثة</div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">1️⃣4️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">Svelte + SvelteKit</div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  المواقع فائقة السرعة، التطبيقات الخفيفة
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  أسرع تقنية حالياً، استهلاك أقل للموارد، أداء ممتاز
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">3-5 أسابيع</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">للمشاريع التي تحتاج سرعة قصوى</div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <div class="card-number">1️⃣5️⃣</div>
            </div>
            <div class="card-body">
              <div class="card-row">
                <div class="card-label">التقنية:</div>
                <div class="card-value tech-stack">Nuxt.js + Supabase</div>
              </div>
              <div class="card-row">
                <div class="card-label">مناسب للمجالات:</div>
                <div class="card-value">
                  التطبيقات الحديثة، الستارت أب التقنية
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">المميزات الأساسية:</div>
                <div class="card-value description">
                  تقنية متقدمة، قاعدة بيانات سحابية، تطوير سريع
                </div>
              </div>
              <div class="card-row">
                <div class="card-label">وقت التطوير:</div>
                <div class="card-value">4-7 أسابيع</div>
              </div>
              <div class="card-row">
                <div class="card-label">التوصية:</div>
                <div class="card-value">للمشاريع التقنية المتقدمة</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
