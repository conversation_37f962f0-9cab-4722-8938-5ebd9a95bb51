#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os

def create_tech_comparison_pdf():
    # Create PDF document
    doc = SimpleDocTemplate(
        "tech-stack-comparison.pdf",
        pagesize=landscape(A4),
        rightMargin=30,
        leftMargin=30,
        topMargin=30,
        bottomMargin=30
    )
    
    # Container for the 'Flowable' objects
    elements = []
    
    # Get styles
    styles = getSampleStyleSheet()
    
    # Create custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1,  # Center alignment
        textColor=colors.HexColor('#2c3e50')
    )
    
    # Add title
    title = Paragraph("مقارنة التقنيات والمنصات للمشاريع البرمجية", title_style)
    elements.append(title)
    elements.append(Spacer(1, 20))
    
    # Table data
    data = [
        ['الرقم', 'الواجهة (Front)', 'الخلفية (Back) / منصة', 'نوع المشروع', 'السعر النسبي 💰', 'الوصف السريع للبيع'],
        ['1️⃣', 'HTML + CSS + JS', 'PHP + Laravel', 'موقع بسيط / شركة', '💸', 'موقع مخصص وسريع – تعريف شركة أو خدمات'],
        ['2️⃣', 'React.js', 'Node.js + Express', 'Web App / متجر إلكتروني', '💵💵', 'حديث، سريع، قابل للتوسعة'],
        ['3️⃣', 'Vue.js', 'Laravel', 'نظام متوسط – مرونة جيدة', '💵💵', 'مزيج ممتاز لمواقع بإدارة بسيطة'],
        ['4️⃣', 'HTML + CSS + JS', 'Python + Flask', 'موقع بسيط فيه تسجيل دخول', '💵', 'كويس لمواقع العضويات الخفيفة'],
        ['5️⃣', 'React.js + Tailwind CSS', 'Django (Python)', 'Web App متكامل', '💵💵💵', 'مثالي للأنظمة الإدارية ولوحات التحكم'],
        ['6️⃣', 'Angular', 'Java + Spring Boot', 'تطبيق حكومي / مؤسسي', '💰💰💰💰', 'للأنظمة المعقدة والمؤمنة جداً'],
        ['7️⃣', 'Next.js', 'Node.js + MongoDB', 'موقع ديناميكي مع SEO عالي', '💵💵', 'موقع حديث وسريع، مثالي لمحركات البحث'],
        ['8️⃣', 'React.js', 'Firebase', 'MVP سريع / تطبيق موبايل', '💵', 'بدون سيرفر، للتجارب والمشاريع الصغيرة'],
        ['9️⃣', 'HTML + Bootstrap', 'لا يوجد Back-End', 'صفحة ثابتة / Landing Page', '💸', 'أرخص وأسرع حل لحملات إعلانية'],
        ['🔟', 'WordPress (تصميم جاهز أو مخصص)', 'PHP', 'موقع محتوى / مدونة / شركة', '💵', 'سهل وسريع – العميل يقدر يديره بنفسه'],
        ['1️⃣1️⃣', 'WooCommerce (إضافة للوردبريس)', 'WordPress + PHP', 'متجر إلكتروني بسيط', '💵💵', 'مثالي للمشاريع الصغيرة والمتوسطة'],
        ['1️⃣2️⃣', 'Shopify', 'SaaS / منصة جاهزة', 'متجر جاهز وسريع التشغيل', '💵💵💵', 'أسرع حل للبيع أونلاين – بدون برمجة']
    ]
    
    # Create table
    table = Table(data, colWidths=[0.8*inch, 2*inch, 2*inch, 2.2*inch, 1.2*inch, 3*inch])
    
    # Table style
    table.setStyle(TableStyle([
        # Header row
        ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498db')),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        
        # Data rows
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 8),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        
        # Alternating row colors
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')]),
        
        # Padding
        ('TOPPADDING', (0, 0), (-1, -1), 6),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ('LEFTPADDING', (0, 0), (-1, -1), 6),
        ('RIGHTPADDING', (0, 0), (-1, -1), 6),
        
        # Valign
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))
    
    elements.append(table)
    elements.append(Spacer(1, 30))
    
    # Add price legend
    legend_style = ParagraphStyle(
        'Legend',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=6,
        textColor=colors.HexColor('#2c3e50')
    )
    
    legend_title = Paragraph("<b>مفتاح الأسعار:</b>", legend_style)
    elements.append(legend_title)
    
    legend_items = [
        "💸 - منخفض جداً",
        "💵 - منخفض", 
        "💵💵 - متوسط",
        "💵💵💵 - مرتفع",
        "💰💰💰💰 - مرتفع جداً"
    ]
    
    for item in legend_items:
        legend_item = Paragraph(item, legend_style)
        elements.append(legend_item)
    
    # Build PDF
    doc.build(elements)
    print("PDF created successfully: tech-stack-comparison.pdf")

if __name__ == "__main__":
    create_tech_comparison_pdf()
